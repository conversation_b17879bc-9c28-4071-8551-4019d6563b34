-- Fix Room Creation Trigger Issue - Comprehensive Fix
-- This script fixes the room creation error by ensuring the correct trigger function is in place

-- Step 1: Drop ALL existing triggers and functions that might be causing conflicts
DROP TRIGGER IF EXISTS trigger_add_room_creator_as_admin ON rooms;
DROP TRIGGER IF EXISTS add_room_creator_trigger ON rooms;
DROP TRIGGER IF EXISTS room_creator_trigger ON rooms;

-- Drop all possible function variations
DROP FUNCTION IF EXISTS add_room_creator_as_admin();
DROP FUNCTION IF EXISTS add_room_creator_as_admin(text);
DROP FUNCTION IF EXISTS add_room_creator_as_admin(uuid);
DROP FUNCTION IF EXISTS add_room_creator();
DROP FUNCTION IF EXISTS room_creator_handler();

-- Step 2: Check for any other functions that might reference 'user_id' incorrectly
DO $$
DECLARE
    func_record RECORD;
BEGIN
    -- Find any functions that might be using NEW.user_id incorrectly
    FOR func_record IN
        SELECT proname, prosrc
        FROM pg_proc
        WHERE prosrc LIKE '%NEW.user_id%'
        AND proname LIKE '%room%'
    LOOP
        RAISE NOTICE 'Found potentially problematic function: % with source containing NEW.user_id', func_record.proname;
        -- Drop the problematic function
        EXECUTE 'DROP FUNCTION IF EXISTS ' || func_record.proname || '() CASCADE';
    END LOOP;
END $$;

-- Step 3: Recreate the correct function with proper error handling
CREATE OR REPLACE FUNCTION add_room_creator_as_admin()
RETURNS TRIGGER AS $$
BEGIN
  -- Validate that NEW record has the required fields
  IF NEW.id IS NULL THEN
    RAISE EXCEPTION 'Room ID cannot be null';
  END IF;

  IF NEW.created_by IS NULL THEN
    RAISE EXCEPTION 'Room creator (created_by) cannot be null';
  END IF;

  -- Insert the room creator as admin in room_members table
  INSERT INTO room_members (room_id, user_id, role)
  VALUES (NEW.id, NEW.created_by, 'admin');

  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    RAISE EXCEPTION 'Error in add_room_creator_as_admin trigger: %', SQLERRM;
END;
$$ LANGUAGE plpgsql;

-- Step 4: Recreate the trigger
CREATE TRIGGER trigger_add_room_creator_as_admin
  AFTER INSERT ON rooms
  FOR EACH ROW
  EXECUTE FUNCTION add_room_creator_as_admin();

-- Step 5: Comprehensive verification
DO $$
BEGIN
  -- Check if created_by column exists
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'rooms' AND column_name = 'created_by'
  ) THEN
    RAISE EXCEPTION 'rooms table is missing created_by column';
  END IF;

  -- Check if room_members table exists and has correct structure
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.tables
    WHERE table_name = 'room_members'
  ) THEN
    RAISE EXCEPTION 'room_members table does not exist';
  END IF;

  -- Verify the trigger exists
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.triggers
    WHERE trigger_name = 'trigger_add_room_creator_as_admin'
  ) THEN
    RAISE EXCEPTION 'Room creation trigger was not created successfully';
  END IF;

  RAISE NOTICE 'Room creation trigger has been fixed successfully';
  RAISE NOTICE 'All conflicting functions and triggers have been removed';
  RAISE NOTICE 'New trigger is properly configured to use NEW.created_by field';
END $$;
